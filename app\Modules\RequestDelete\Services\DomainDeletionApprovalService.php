<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Modules\RequestDelete\Jobs\SendDomainDeleteRequestEmail;
use App\Events\DomainHistoryEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainDeletionApprovalService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function handleApproval(array $data, array $adminContext, string $supportNote): void
    {
        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success', 'Domain deletion request approved by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');

        $this->markAsApproved($data, $adminContext, $supportNote);

        $registeredDomainId = $this->getRegisteredDomainId($data['domainId']);
        DomainRegistrationRefundLimitService::instance()->incrementCounter($registeredDomainId);

        if ($this->isRecentlyRegisteredDomain($data['domainId'])) {
            $this->processImmediateDeletion($data, $adminContext, $supportNote);
        } else {
            $this->processNormalApproval($data, $adminContext, $supportNote);
        }

        $this->sendUserNotification($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete.');
        $this->sendUserEmail(
            $data,
            'Domain Deletion Request Approved',
            'This email is to confirm that you have requested to delete the domain with the following details:',
            'If you did not request this deletion, please contact our support team immediately to cancel the process.',
            'If you wish to proceed, no further action is required—your request will be completed as scheduled.'
        );
    }

    public function markAsApproved(array $data, array $adminContext, string $supportNote): void
    {
        $registeredDomainId = $this->getRegisteredDomainId($data['domainId']);
        $refundStatus = $this->calculateRefundStatusForApproval($data['domainId'], $registeredDomainId);
        $approvalData = $this->buildApprovalData($adminContext, $supportNote, $refundStatus);
        DatabaseQueryService::instance()->updateDomainCancellationRequest($registeredDomainId, $approvalData);
    }

    private function calculateRefundStatusForApproval(string $domainId, int $registeredDomainId): array
    {
        $requestInfo = DatabaseQueryService::instance()->getRequestDetailsForRenewal($registeredDomainId);
        return DatabaseQueryService::instance()->calculateRefundAndRenewalStatus($domainId, $requestInfo->requested_at);
    }

    private function buildApprovalData(array $adminContext, string $supportNote, array $refundStatus): array
    {
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";

        return [
            'support_agent_id' => $adminContext['id'],
            'support_agent_name' => $adminFullName,
            'feedback_date' => now(),
            'support_note' => $supportNote,
            'is_refunded' => $refundStatus['is_refunded'],
            'is_renewal' => $refundStatus['is_renewal'],
            'deleted_at' => now(),
        ];
    }

    private function processImmediateDeletion(array $data, array $adminContext, string $supportNote): void
    {
        $this->dispatchDeletionJob($data);
        $this->sendImmediateApprovalNotifications($data);
    }

    private function processNormalApproval(array $data, array $adminContext, string $supportNote): void
    {
        $this->sendNormalApprovalNotifications($data);
    }

    private function dispatchDeletionJob(array $data): void
    {
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $data['supportNote'],
            $data['adminId'],
            $data['adminName'],
            $data['adminEmail'],
            0
        );
    }

    private function sendImmediateApprovalNotifications(array $data): void
    {
        $this->sendUserNotification($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved and is being processed immediately.');
        $this->sendUserEmail(
            $data,
            'Domain Deletion Request Approved',
            'This email is to confirm that you have requested to delete the domain with the following details:',
            'Your domain deletion is being processed immediately as it was recently registered.',
            'If you did not request this deletion, please contact our support team immediately.'
        );
    }

    private function sendNormalApprovalNotifications(array $data): void
    {
        $this->sendUserNotification($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete.');
        $this->sendUserEmail(
            $data,
            'Domain Deletion Request Approved',
            'This email is to confirm that you have requested to delete the domain with the following details:',
            'If you did not request this deletion, please contact our support team immediately to cancel the process.',
            'If you wish to proceed, no further action is required—your request will be completed as scheduled.'
        );
    }

    private function sendUserNotification(array $data, string $title, string $message): void
    {
        DB::client()->table('notifications')->insert([
            'user_id' => $data['userId'],
            'title' => $title,
            'message' => $message,
            'redirect_url' => '/domain',
            'created_at' => now(),
            'updated_at' => now(),
            'importance' => 'important',
        ]);
    }

    private function sendUserEmail(array $data, string $subject, string $body, ?string $body2 = null, ?string $body3 = null, ?string $body4 = null): void
    {
        if (!isset($data['userEmail']) || !isset($data['domainName']))
            return;

        SendDomainDeleteRequestEmail::dispatch($data, $subject, $body, $body2, $body3, $body4);
    }

    private function logDomainHistory(array $data, string $action, string $status, string $message): void
    {
        DomainHistoryEvent::dispatch($data['domainId'], $action, $status, $message);
    }

    private function getRegisteredDomainId(string $domainId): int
    {
        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        if (!$registeredDomain) {
            app(AuthLogger::class)->error("Registered domain not found for domainId: {$domainId}.");
            throw new \Exception("Registered domain not found for domain ID: {$domainId}");
        }

        return $registeredDomain->id;
    }

    private function isRecentlyRegisteredDomain(string $domainId): bool
    {
        $domain = DB::client()->table('domains')->where('id', $domainId)->first();
        
        if (!$domain) {
            return false;
        }

        $createdAt = Carbon::parse($domain->created_at);
        return $createdAt->diffInDays($this->now) <= 5;
    }
}
